import logging

from pydantic import computed_field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """
    Application settings loaded from environment variables or a .env file.
    """

    APP_TITLE: str = "Playlist Intelligence Agent MVP"
    API_VERSION: str = "0.1.0"
    LOG_LEVEL: str = "INFO"
    FRONTEND_URL: str = "http://localhost:5173"
    APP_ENV: str = "development"

    DB_USER: str = "admin"
    DB_PASSWORD: str = "password"
    DB_HOST: str = "localhost"
    DB_PORT: int = 5432
    DB_NAME: str = "playlist_agent_dev"
    DB_ECHO: bool = False

    SPOTIFY_CLIENT_ID: str | None = None
    SPOTIFY_CLIENT_SECRET: str | None = None
    SPOTIFY_REDIRECT_URI: str | None = None

    TOKENS_ENCRYPTION_KEY: str | None = None
    JWT_SECRET_KEY: str | None = None
    JWT_ALGORITHM: str = "HS256"
    JWT_EXPIRY_MINUTES: int = 60

    @computed_field
    @property
    def DATABASE_URL_ASYNC(self) -> str:
        return f"postgresql+asyncpg://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"

    @computed_field
    @property
    def DATABASE_URL_SYNC(self) -> str:
        return f"postgresql+psycopg2://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"

    @field_validator(
        "SPOTIFY_CLIENT_ID",
        "SPOTIFY_CLIENT_SECRET",
        "SPOTIFY_REDIRECT_URI",
        "TOKENS_ENCRYPTION_KEY",
        "JWT_SECRET_KEY",
    )
    @classmethod
    def check_required_settings(cls, v: str | None) -> str:
        if not v:
            raise ValueError("This environment variable must be set.")
        return v

    model_config = SettingsConfigDict(env_file=".env", extra="ignore")

    def get_log_level(self) -> int:
        """Converts LOG_LEVEL string to logging level integer."""
        return getattr(logging, self.LOG_LEVEL.upper(), logging.INFO)


settings = Settings()
