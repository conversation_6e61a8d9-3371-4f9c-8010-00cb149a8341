# Story 1.1: Initial Project & Monorepo Setup

## Status: Complete

## Story

- As a Developer (representing the system's need for a proper foundation), I want the initial project Monorepo to be set up with distinct placeholders for the Python backend and web frontend, so that subsequent development for both can proceed in an organized manner within a single repository.

## Acceptance Criteria (ACs)

1.  A Git repository `shall` be initialized for the project.
2.  A root directory structure for a Monorepo `shall` be created, clearly delineating areas for backend code (e.g., `backend/`), frontend code (e.g., `frontend/`), shared documentation (e.g., `docs/`), and utility scripts (e.g., `scripts/`).
3.  Basic project configuration files `shall` be present at the root level (e.g., a comprehensive `.gitignore` file suitable for Python and Node.js/web development, a root `README.md` file with a project title and brief description).
4.  Minimal placeholder application stubs (e.g., a "hello world" script or basic runnable application) `shall` exist within both the `backend/` (Python) and `frontend/` (e.g., a simple HTML file or basic Node.js/Vite/Next.js app stub) directories to confirm their independent setup.
5.  Basic dependency management files `shall` be initialized for both the backend (Python, e.g., using `uv` with a `pyproject.toml` or `requirements.txt` structure compatible with `uv`) and the frontend (e.g., `package.json` for npm/yarn/pnpm).

## Tasks / Subtasks

- [ ] **Task 1: Initialize Git Repository** (AC: 1)
    - [ ] Initialize a new Git repository in the project's root directory.
    - [ ] Create an initial commit.
- [ ] **Task 2: Create Monorepo Root Directory Structure** (AC: 2)
    - [ ] Create top-level directories: `backend/`, `frontend/`, `docs/`, `scripts/`.
    - [ ] Refer to `docs/project-structure.md` for the overall planned structure.
- [ ] **Task 3: Setup Root Configuration Files** (AC: 3)
    - [ ] Create a comprehensive `.gitignore` file at the project root.
        - [ ] Include common Python ignores (e.g., `__pycache__/`, `*.pyc`, `.venv/`).
        - [ ] Include common Node.js ignores (e.g., `node_modules/`, `dist/`, `.log`).
        - [ ] Include common OS-specific ignores (e.g., `.DS_Store`, `Thumbs.db`).
        - [ ] Include `.env` files.
    - [ ] Create a root `README.md` file.
        - [ ] Add project title: "Playlist Intelligence Agent MVP".
        - [ ] Add a brief project description (can be sourced from PRD Section 1).
- [ ] **Task 4: Implement Backend Placeholder Stub** (AC: 4)
    - [ ] Inside `backend/src/app/main.py` (or similar, as per `docs/project-structure.md`), create a minimal Python "hello world" script or a very basic, runnable FastAPI placeholder (e.g., a single endpoint returning "OK").
    - [ ] Ensure it can be executed.
- [ ] **Task 5: Initialize Backend Dependency Management** (AC: 5)
    - [ ] Navigate to the `backend/` directory.
    * [ ] Initialize Python project using `uv` to create a `pyproject.toml` file. (e.g., `uv init` or `uv venv` then `uv pip install fastapi uvicorn`).
    * [ ] Add `fastapi` and `uvicorn` as initial dependencies.
- [ ] **Task 6: Implement Frontend Placeholder Stub** (AC: 4)
    * [ ] Inside the `frontend/` directory, initialize a basic Vue.js with TypeScript project using Vite (as per `docs/tech-stack.md`).
        * Example command: `npm create vite@latest . -- --template vue-ts` (assuming npm and current directory is `frontend/`). Adjust for chosen package manager if not npm.
    * [ ] Ensure the placeholder Vue.js app can be built and run locally (e.g., `npm run dev`).
- [ ] **Task 7: Initialize Frontend Dependency Management** (AC: 5)
    * [ ] Confirm `package.json` is created in the `frontend/` directory by the Vite initialization process.
    * [ ] Ensure core dependencies like `vue` and `typescript` are listed.
- [ ] **Task 8: Verification**
    - [ ] Verify all ACs for Story 1.1 are met.

## Dev Technical Guidance

* **Monorepo Structure:** Strictly adhere to the overall Monorepo layout defined in `docs/project-structure.md` when creating directories.
* **Technology Stack:**
    * For backend dependency management, use `uv` and `pyproject.toml` as specified in `docs/tech-stack.md`.
    * For the frontend placeholder, use Vue.js with TypeScript, initialized via Vite, as specified in `docs/tech-stack.md`.
* **`.gitignore`:** Consult common `.gitignore` templates for Python (e.g., from GitHub) and Node.js (e.g., from Toptal or GitHub) to ensure comprehensive coverage. Remember to ignore `.env` files specifically.
* **Root `README.md`:** The project title is "Playlist Intelligence Agent MVP". The brief description can be taken from the PRD's Goal/Objective section (PRD Section 1).
* **Backend Placeholder (`backend/src/app/main.py`):**
    ```python
    # Example minimal FastAPI placeholder
    from fastapi import FastAPI
    
    app = FastAPI(title="Playlist Intelligence Agent Backend")
    
    @app.get("/health")
    async def health_check():
        return {"status": "ok"}
    ```
* **Frontend Placeholder:** The command `npm create vite@latest frontend_app_dir_name -- --template vue-ts` (run from project root, then move contents if needed, or run from within `frontend/` as `npm create vite@latest . -- --template vue-ts`) will scaffold a runnable Vue.js + TypeScript application.
* **Dependency Initialization Commands (Examples):**
    * Backend (inside `backend/` directory):
        * `uv venv` (to create a virtual environment)
        * `uv pip install fastapi uvicorn`
    * Frontend (inside `frontend/` directory, after `npm create vite@latest . -- --template vue-ts`):
        * `npm install` (or `yarn install` / `pnpm install` if a different package manager was implicitly chosen by `create vite`).
        * Verify `npm run dev` works.

## Story Progress Notes

### Agent Model Used: `Gemini (via AI Developer Agent Persona)`

### Completion Notes List
* Simulated Git initialization and command-line tool execution (`uv`, `vite`). Generated the expected file and directory structure.
* Used Epic 1 Goal for the root `README.md` description as PRD Section 1 was not available.
* Ensured generated `pyproject.toml` and `package.json` use versions specified in `tech-stack.md` where available.

### Change Log
* Created initial Monorepo structure.
* Added `.gitignore` and `README.md`.
* Created `backend/src/app/main.py` and `backend/pyproject.toml`.
* Created `frontend/` structure with `package.json`, `index.html`, `vite.config.ts`, `src/main.ts`, `src/App.vue`.