# Story 1.2: Backend API Scaffolding (FastAPI)

## Status: Draft

## Story

- As a Developer, I want a basic FastAPI application scaffolded for the backend, including initial configuration for environment variables, so that API endpoints can be progressively added and the backend can serve as the central logic hub.

## Acceptance Criteria (ACs)

1.  The FastAPI framework and its core dependencies (like Uvicorn for serving) `shall` be added as dependencies to the Python backend project (e.g., in the `pyproject.toml` managed by `uv`).
2.  A minimal FastAPI application `shall` be created within the `backend/src/app/` directory structure.
3.  This application `shall` include at least one basic, unauthenticated health check endpoint (e.g., `/health`) that returns a simple success response (e.g., `{"status": "ok"}`).
4.  The backend FastAPI application `shall` be runnable locally using a development server (e.g., Uvicorn command like `uvicorn app.main:app --reload`).
5.  A basic structure for organizing the FastAPI application `shall` be established (e.g., a main application file `main.py`, and potentially an initial `api/` or `routers/` subdirectory for future endpoint organization as per `docs/project-structure.md`).
6.  A mechanism for managing environment variables and application configuration (e.g., using Pydantic settings to load from `.env` files or system environment variables) `shall` be implemented in `backend/src/app/config.py` and demonstrated for basic settings like application title or API version.
7.  A basic, structured logging mechanism (e.g., using Python's standard `logging` module configured for a clear, consistent output format like JSON) `shall` be integrated into the FastAPI application, allowing for configurable log levels (e.g., `DEBUG`, `INFO`, `ERROR`) and basic request/application event logging.
8.  A basic framework for centralized API exception handling `shall` be established within the FastAPI application (e.g., by implementing a few common custom exception handlers) to promote consistent JSON error responses for typical HTTP errors (such as 400, 401, 403, 404, 500).

## Tasks / Subtasks

- [ ] **Task 1: Add FastAPI Dependencies** (AC: 1)
    - [ ] Navigate to the `backend/` directory.
    - [ ] Using `uv pip install`, add `fastapi`, `uvicorn[standard]` (for standard server features), and `pydantic` (explicitly, though FastAPI uses it) to the `pyproject.toml` file.
- [ ] **Task 2: Create Minimal FastAPI Application** (AC: 2, 5)
    - [ ] Create `backend/src/app/main.py`.
    - [ ] In `main.py`, initialize a `FastAPI()` app instance.
    - [ ] Create an initial `backend/src/app/api/` (or `routers/`) directory for organizing future API endpoints.
- [ ] **Task 3: Implement Health Check Endpoint** (AC: 3)
    - [ ] In `main.py` (or a new router in `api/`), implement a GET endpoint at `/health`.
    - [ ] Ensure it returns `{"status": "ok"}` with a 200 HTTP status.
- [ ] **Task 4: Implement Configuration Management** (AC: 6)
    - [ ] Create `backend/src/app/config.py`.
    - [ ] Implement a Pydantic `BaseSettings` class to load settings like `APP_TITLE: str = "Playlist Intelligence Agent MVP"` and `API_VERSION: str = "0.1.0"` from environment variables or a `.env` file.
    - [ ] Ensure the FastAPI app can access these settings (e.g., by creating an instance of the settings class).
    - [ ] Create/update `backend/.env.example` with these example variables. Add `backend/.env` to `backend/.gitignore` (if not covered by root `.gitignore`).
- [ ] **Task 5: Integrate Basic Structured Logging** (AC: 7)
    - [ ] Configure Python's standard `logging` module in `main.py` or a dedicated logging setup module.
    - [ ] Aim for JSON formatted logs if simple to set up (for platform compatibility, see `docs/operational-guidelines.md` - Error Handling section). If complex, a clear text format with timestamp, level, logger name, and message is acceptable for MVP.
    - [ ] Log an INFO message upon application startup and shutdown.
    - [ ] Implement middleware or a dependency to log basic request information (method, path, response status).
- [ ] **Task 6: Implement Basic Centralized Exception Handling** (AC: 8)
    - [ ] In `main.py`, define a base custom exception (e.g., `AppException(Exception)`).
    * [ ] Add a FastAPI exception handler for this `AppException` to return a generic JSON response (e.g., `{"detail": "An application error occurred"}`).
    * [ ] Add a FastAPI exception handler for `fastapi.exceptions.RequestValidationError` to customize the 422 response if desired (though default is often sufficient).
    * [ ] Add a generic exception handler for unhandled `Exception` to return a 500 error with a generic message, ensuring no stack traces are exposed in the response.
- [ ] **Task 7: Ensure Local Runnability & Update README** (AC: 4)
    - [ ] Verify the application can be run locally using `uvicorn app.main:app --reload --host 0.0.0.0 --port 8000` (or similar) from within `backend/src/`.
    - [ ] Update `backend/README.md` with clear instructions on how to set up the environment (using `uv`), install dependencies, and run the development server.
- [ ] **Task 8: Verification**
    - [ ] Test the `/health` endpoint.
    - [ ] Check log output for startup and request logs.
    - [ ] Verify configuration settings are loaded.
    - [ ] Trigger a known validation error and a generic error to test exception handlers.
    - [ ] Ensure all ACs are met.

## Dev Technical Guidance

* **Technology Versions:** Refer to `docs/tech-stack.md` for specified versions of `FastAPI`, `Uvicorn`, `Pydantic`, and `uv`.
* **Project Structure:** All backend code should reside within `backend/src/app/` as detailed in `docs/project-structure.md`. Place `main.py` at `backend/src/app/main.py`. Routers should go into `backend/src/app/api/`. Configuration in `backend/src/app/config.py`.
* **Environment Variables:** Use Pydantic's `BaseSettings` for loading from `.env` and environment variables. See `docs/environment-vars.md` for strategy.
    * Example `backend/src/app/config.py`:
      ```python
      from pydantic_settings import BaseSettings, SettingsConfigDict
      
      class Settings(BaseSettings):
          APP_TITLE: str = "Playlist Intelligence Agent MVP"
          API_VERSION: str = "0.1.0"
          # Add other settings here as needed, e.g., for database, API keys
          
          model_config = SettingsConfigDict(env_file=".env", extra="ignore")

      settings = Settings()
      ```
* **Logging:**
    * Aim for JSON output if straightforward with the standard `logging` module. Example for basic text format: `logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')`.
    * Refer to `docs/operational-guidelines.md` (Error Handling Strategy section) for guidance on log format and levels.
* **Exception Handling:**
    * Define a base `AppException` in a common module (e.g., `app/core/exceptions.py`).
    * Use FastAPI's `@app.exception_handler()` decorator. Example:
      ```python
      # In app/main.py
      from fastapi import FastAPI, Request
      from fastapi.responses import JSONResponse
      # from .core.exceptions import AppException # Assuming you create this

      # class AppException(Exception):
      #     def __init__(self, status_code: int, detail: str):
      #         self.status_code = status_code
      #         self.detail = detail

      app = FastAPI() # ...

      # @app.exception_handler(AppException)
      # async def app_exception_handler(request: Request, exc: AppException):
      #     return JSONResponse(
      #         status_code=exc.status_code,
      #         content={"detail": exc.detail},
      #     )

      # @app.exception_handler(Exception) # Generic handler
      # async def generic_exception_handler(request: Request, exc: Exception):
      #     # Log the full error internally here
      #     return JSONResponse(
      #         status_code=500,
      #         content={"detail": "An unexpected error occurred."},
      #     )
      ```
* **Uvicorn Command:** To run from the `backend/src/` directory: `uvicorn app.main:app --reload --host 0.0.0.0 --port 8000`. Adjust path to `app.main:app` if running from `backend/`.
* **Dependencies (`pyproject.toml` via `uv`):**
    ```toml
    [project]
    name = "playlist-intelligence-agent-backend"
    version = "0.1.0"
    dependencies = [
        "fastapi",
        "uvicorn[standard]", # Includes httptools and websockets
        "pydantic",
        "pydantic-settings",
        # Add other dependencies as they become necessary
    ]
    # ... other pyproject.toml fields like [tool.uv] if needed
    ```

## Story Progress Notes

### Agent Model Used: `Gemini (via AI Developer Agent Persona)`

### Completion Notes List
* Implemented FastAPI scaffolding following `project-structure.md`.
* Added `pydantic-settings` for configuration management.
* Added `python-json-logger` to meet the JSON logging requirement from `operational-guidelines.md`.
* Established `api` directory with a health check router.
* Implemented basic logging middleware and central exception handlers.
* Created `backend/README.md` with setup/run instructions.
* Added `backend/.env.example`.

### Change Log
* Updated `backend/pyproject.toml`.
* Created `backend/.env.example`.
* Created `backend/src/app/config.py`.
* Created `backend/src/app/logging_config.py`.
* Created `backend/src/app/core/exceptions.py`.
* Created `backend/src/app/api/health.py`.
* Updated `backend/src/app/main.py`.
* Created `backend/README.md`.