# Story 1.3: Initial Database Setup (PostgreSQL + pgvector)

## Status: Complete

## Story

- As a System Administrator (representing the system's data persistence needs), I want an initial PostgreSQL database (or a connection to a managed service) to be set up, including the `pgvector` extension and basic backend connectivity, so that application data (like user tokens, playlist DNA, track info) can eventually be stored and queried.

## Acceptance Criteria (ACs)

1.  A PostgreSQL database instance `shall` be accessible for the development environment.
2.  This can be a locally running instance (e.g., via Docker) or a connection to a cloud-hosted free-tier PostgreSQL service that is compatible with the `pgvector` extension (as identified in our research document and specified in `docs/tech-stack.md` as targeting Koyeb for hosting, which can support this).
3.  The `pgvector` extension `shall` be successfully installed and enabled in the target PostgreSQL database, confirming its availability for vector operations.
4.  Basic database connection logic `shall` be implemented within the Python backend (FastAPI application created in Story 1.2).
5.  This logic will allow the application to connect to the configured PostgreSQL database.
6.  An Object-Relational Mapper (ORM) (`SQLAlchemy`) and an associated database migration tool (`Alembic`) `shall` be integrated into the backend project and configured to connect to the PostgreSQL database.
7.  An initial Alembic migration `shall` be created and successfully applied to establish at least one placeholder table (e.g., an `app_settings` or a minimal `users_stub` table), thereby verifying the complete database interaction and schema management workflow. This initial migration should also ensure the `pgvector` extension is created if it doesn't exist.
8.  Database connection credentials (username, password, host, port, database name) `shall` be managed securely through environment variables loaded by the backend's configuration system (`config.py` from Story 1.2) and not hardcoded.

## Tasks / Subtasks

- [ ] **Task 1: Setup PostgreSQL Instance with pgvector** (AC: 1, 2, 3)
    - [ ] If using local Docker for development:
        - [ ] Add PostgreSQL with `pgvector` support to the project's `docker-compose.yml` (this file might need to be created at the project root or in `backend/`).
        - [ ] Configure default user, password, and database name.
        - [ ] Ensure the `pgvector` extension is enabled in the Docker setup.
    - [ ] If using a cloud-hosted service (e.g., Koyeb's PostgreSQL for dev/testing):
        - [ ] Provision a free-tier PostgreSQL instance.
        - [ ] Verify `pgvector` extension compatibility and enable it if necessary.
    - [ ] Confirm connectivity to the database instance using a standard SQL client.
- [ ] **Task 2: Add Database Dependencies to Backend** (AC: 6)
    - [ ] Navigate to the `backend/` directory.
    - [ ] Using `uv pip install`, add `sqlalchemy`, `alembic`, and a suitable PostgreSQL driver (`asyncpg` is recommended for FastAPI's async capabilities) to `pyproject.toml`.
- [ ] **Task 3: Configure Database Connection in Backend** (AC: 4, 5, 8)
    - [ ] Update `backend/src/app/config.py`:
        - [ ] Add Pydantic settings for database connection parameters (e.g., `DATABASE_URL` or individual params like `DB_USER`, `DB_PASSWORD`, `DB_HOST`, `DB_PORT`, `DB_NAME`).
    - [ ] Create `backend/.env.example` (if not present from Story 1.2, or update it) to include these database connection variables.
    - [ ] Create/Update `backend/.env` (and ensure it's in `backend/.gitignore`) with actual development DB credentials.
    - [ ] Implement basic SQLAlchemy engine creation and session management logic (e.g., in `backend/src/app/db/session.py` or similar). This should use the connection string from `config.py`.
- [ ] **Task 4: Initialize and Configure Alembic** (AC: 6)
    - [ ] From the `backend/` directory, initialize Alembic (e.g., `alembic init alembic`). This will create an `alembic/` directory and `alembic.ini` file.
    - [ ] Modify `alembic.ini` to point to the database URL from the application's configuration (via `config.py`).
    - [ ] Modify `alembic/env.py`:
        - [ ] Import necessary modules (SQLAlchemy Base, models if the placeholder table model is created early).
        - [ ] Set `target_metadata` to your SQLAlchemy declarative base.
        - [ ] Configure it to use the database URL from the application settings for migration context.
- [ ] **Task 5: Create Initial Alembic Migration** (AC: 3, 7)
    - [ ] Create a SQLAlchemy model for a simple placeholder table (e.g., `app_settings` with `key` and `value` columns, or a `users_stub` with an `id` and `email`).
    - [ ] Generate an initial Alembic migration script (e.g., `alembic revision -m "create_initial_tables_and_pgvector"`).
    - [ ] Edit the generated migration script:
        - [ ] Add the SQL command `CREATE EXTENSION IF NOT EXISTS vector;` at the beginning of the `upgrade()` function.
        - [ ] Add Alembic operations to create the placeholder table (e.g., `op.create_table(...)`).
        - [ ] Ensure the `downgrade()` function correctly drops the placeholder table.
- [ ] **Task 6: Apply Migration and Verify** (AC: 7)
    - [ ] Run `alembic upgrade head` to apply the migration to the database.
    - [ ] Connect to the database using a SQL client and verify:
        - [ ] The `pgvector` extension is active.
        - [ ] The placeholder table exists with the correct schema.
    - [ ] Write a simple test script or add a temporary FastAPI endpoint that attempts to connect to the DB and query the placeholder table to verify application connectivity.
- [ ] **Task 7: Update Backend README**
    - [ ] Add instructions to `backend/README.md` on how to:
        - [ ] Set up the local database (e.g., start Docker Compose service).
        - [ ] Run Alembic migrations.
- [ ] **Task 8: Verification**
    - [ ] Ensure all ACs for Story 1.3 are met.

## Dev Technical Guidance

* **PostgreSQL & pgvector:**
    * Use PostgreSQL version 16.2 or compatible, as specified in `docs/tech-stack.md`.
    * For local Docker setup, an image like `pgvector/pgvector:pg16` or `ankane/pgvector` can be used in `docker-compose.yml`.
      Example `docker-compose.yml` snippet (place at project root or `backend/`):
      ```yaml
      # version: '3.8' # Optional
      services:
        db:
          image: pgvector/pgvector:pg16 # Or ankane/pgvector
          container_name: playlist_agent_db
          environment:
            POSTGRES_USER: ${DB_USER:-admin}
            POSTGRES_PASSWORD: ${DB_PASSWORD:-password}
            POSTGRES_DB: ${DB_NAME:-playlist_agent_dev}
          ports:
            - "${DB_PORT:-5432}:5432"
          volumes:
            - postgres_data:/var/lib/postgresql/data
      volumes:
        postgres_data:
      ```
* **Dependencies:** Add to `backend/pyproject.toml` using `uv pip install`:
    * `sqlalchemy`
    * `alembic`
    * `asyncpg` (for async SQLAlchemy with FastAPI)
    * `psycopg2-binary` (if synchronous SQLAlchemy operations are ever needed, or for Alembic if it doesn't use async driver). *Clarification*: Alembic typically uses a sync driver. `asyncpg` is for the application.
* **SQLAlchemy Setup (Async example for FastAPI in `backend/src/app/db/session.py`):**
    ```python
    from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
    from sqlalchemy.orm import sessionmaker, declarative_base
    from app.config import settings # From Story 1.2

    DATABASE_URL = settings.DATABASE_URL # Construct this in config.py from individual DB params

    engine = create_async_engine(DATABASE_URL, echo=settings.DB_ECHO) # Add DB_ECHO to config
    AsyncSessionLocal = sessionmaker(
        autocommit=False, autoflush=False, bind=engine, class_=AsyncSession
    )
    Base = declarative_base()

    async def get_db() -> AsyncSession:
        async with AsyncSessionLocal() as session:
            yield session
    ```
    (Ensure `DATABASE_URL` is correctly assembled in `config.py`, e.g., `f"postgresql+asyncpg://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"`)
* **Alembic Configuration (`alembic/env.py`):**
    * Make sure `target_metadata = Base.metadata` (where `Base` is your SQLAlchemy declarative base).
    * Configure `url` in `get_url()` function to use `settings.DATABASE_URL` (sync version, e.g. `postgresql+psycopg2://...` for Alembic). Alembic runs migrations synchronously.
* **Initial Migration (`alembic/versions/xxxx_create_initial_tables.py`):**
    ```python
    # In the upgrade() function:
    op.execute("CREATE EXTENSION IF NOT EXISTS vector")
    op.create_table(
        'app_settings_stub', # Example placeholder table
        sa.Column('id', sa.Integer, primary_key=True),
        sa.Column('setting_name', sa.String(50), nullable=False, unique=True),
        sa.Column('setting_value', sa.String(255)),
        sa.Column('created_at', sa.TIMESTAMP(timezone=True), server_default=sa.func.now()),
        sa.Column('updated_at', sa.TIMESTAMP(timezone=True), server_default=sa.func.now(), onupdate=sa.func.now())
    )
    # In the downgrade() function:
    op.drop_table('app_settings_stub')
    # op.execute("DROP EXTENSION IF EXISTS vector") # Optional, consider if it should be dropped
    ```
* **Environment Variables for DB:** Add `DB_USER`, `DB_PASSWORD`, `DB_HOST`, `DB_PORT`, `DB_NAME` to `backend/.env.example` and `backend/.env`. Refer to `docs/environment-vars.md`.
* **Driver Note:** `SQLAlchemy` needs a DBAPI driver. For async with FastAPI, `asyncpg` is appropriate. For Alembic (which runs synchronously), `psycopg2-binary` might be needed if `asyncpg` isn't suitable for its operations. Ensure `pyproject.toml` includes the correct driver(s).

## Story Progress Notes

### Agent Model Used: `Gemini (via AI Developer Agent Persona)`

### Completion Notes List
* Implemented PostgreSQL + pgvector setup via Docker Compose.
* Added necessary SQLAlchemy, Alembic, and driver dependencies.
* Configured database connection handling via `config.py` and `db/session.py`.
* Set up Alembic with `alembic.ini` and a custom `env.py`.
* Created an initial migration including `pgvector` and a placeholder table.
* Added a DB health check endpoint for verification.
* Updated `backend/README.md` with setup instructions.
* Added `psycopg2-binary` for Alembic's synchronous operations.

### Change Log
* Created `docker-compose.yml`.
* Created root `.env.example`.
* Updated `backend/pyproject.toml`.
* Updated `backend/.env.example`.
* Updated `backend/src/app/config.py`.
* Created `backend/src/app/db/session.py`.
* Created `backend/src/app/core/models/base.py`.
* Created `backend/src/app/core/models/app_settings.py`.
* Created `backend/alembic.ini`.
* Created `backend/alembic/env.py`.
* Created `backend/alembic/script.py.mako`.
* Created `backend/alembic/versions/a1b2c3d4e5f6_create_initial_tables_and_pgvector.py`.
* Updated `backend/src/app/api/health.py`.
* Updated `backend/README.md`.