# Story 1.6: Epic 1 Backend Testing

## Status: In Progress

## Story

- As a Developer, I want comprehensive unit and integration tests for all backend functionalities developed in Epic 1 (specifically Stories 1.2, 1.3, 1.4, and 1.5), so that I can ensure code quality, verify correct behavior against requirements, and confidently refactor or build upon this foundation later.

## Acceptance Criteria (ACs)

1.  Unit tests (`Pytest`) `shall` be implemented for all critical Python functions/methods in the backend services and utilities created or significantly modified in Stories 1.2, 1.3, 1.4, and 1.5. This specifically includes:
    * Configuration loading logic in `app/config.py`.
    * Database session management utilities in `app/db/session.py`.
    * Token encryption/decryption utility functions in `app/core/security.py`.
    * Core business logic within `SpotifyIntegrationService` methods (e.g., data mapping from Spotipy responses to DTOs, pagination handling logic), with actual `spotipy.Spotify` client calls fully mocked.
    * User repository methods for creating/updating user records and managing their Spotify tokens (mocking database interactions).
2.  Integration tests (`Pytest` with FastAPI `TestClient`) `shall` be implemented for all backend API endpoints defined or scaffolded in Epic 1:
    * `GET /health` (from Story 1.2).
    * `GET /api/v1/auth/login` (from Story 1.4) – Verifying correct redirection.
    * `GET /api/v1/auth/callback` (from Story 1.4) – Simulating Spotify's callback and verifying internal logic (user creation/update, token storage (mocked DB for this part), app JWT cookie setting, and frontend redirect).
    * `GET /api/v1/playlists` (from Story 1.5) – Mocking the `SpotifyIntegrationService` to test the endpoint logic.
    * `GET /api/v1/playlists/{playlist_id}/details` (from Story 1.5) – Mocking the `SpotifyIntegrationService` to test the endpoint logic.
3.  Specific integration tests for the `SpotifyIntegrationService` methods `shall` be created to validate contracts with the *live Spotify API* for read-only operations, ensuring correct parsing of actual API responses. These tests will cover:
    * Fetching user playlists (`get_user_playlists` method, verifying against `UserPlaylistsResponseDto` and `SpotifyPlaylistSummaryDto`).
    * Fetching details of a specific playlist, including its tracks and `snapshot_id` (`get_playlist_tracks_details` method, verifying against `PlaylistTracksDetailResponseDto` [as defined in Story 1.5 draft]).
4.  All implemented unit and integration tests `shall` pass successfully when run in the local development environment.
5.  Test coverage reports (e.g., via `pytest-cov`) for the new backend code in Epic 1 `shall` be generated and reviewed to ensure critical logic paths, common use cases, and relevant edge cases are covered. (While a strict percentage isn't an MVP NFR, good coverage is expected as per Main Arch Doc Sec 14).
6.  The basic testing infrastructure (`pytest` setup, `pytest-cov` configuration, test directory structure) `shall` be established in the `backend/` project.

## Tasks / Subtasks

- [ ] **Task 1: Setup Testing Framework and Configuration** (AC: 6)
    - [ ] Ensure `pytest`, `pytest-cov`, `pytest-asyncio`, and `httpx` are added to `backend/pyproject.toml` as development dependencies.
    - [ ] Configure `pyproject.toml [tool.pytest.ini_options]` for test discovery and async support.
    - [ ] Create the initial test directory structure: `backend/tests/unit/` and `backend/tests/integration/`.
- [ ] **Task 2: Write Unit Tests for Core Utilities & Services** (AC: 1)
    - [ ] Create `backend/tests/unit/core/test_config.py`: Test Pydantic settings loading from environment variables and `.env` files for `app.config.Settings`.
    - [ ] Create `backend/tests/unit/core/test_security.py`: Test token encryption and decryption functions (from Story 1.4 Task 3).
    - [ ] Create `backend/tests/unit/integrations/test_spotify_service.py`:
        - [ ] Test `SpotifyIntegrationService.get_user_playlists` (mock `spotipy.Spotify().current_user_playlists()`, test DTO mapping, test pagination accumulation logic if any).
        - [ ] Test `SpotifyIntegrationService.get_playlist_tracks_details` (mock `spotipy.Spotify().playlist()` and `playlist_items()`, test DTO mapping, test pagination).
    - [ ] Create `backend/tests/unit/repositories/test_user_repository.py`: Test methods for creating/updating user, storing/retrieving tokens (mock database session calls).
- [ ] **Task 3: Write Integration Tests for API Endpoints (with Mocks)** (AC: 2)
    - [ ] Create `backend/tests/integration/api/test_main_endpoints.py`: Test `GET /health` using FastAPI `TestClient`.
    - [ ] Create `backend/tests/integration/api/test_auth_endpoints.py`:
        - [ ] Test `GET /api/v1/auth/login`: Verify it returns a 307 redirect and the `Location` header points to Spotify. Mock `SpotifyOAuth().get_authorize_url()`.
        - [ ] Test `GET /api/v1/auth/callback`:
            - [ ] Mock `SpotifyOAuth().get_access_token()` to return mock tokens.
            - [ ] Mock Spotipy client calls for user profile fetching.
            - [ ] Mock user data service/repository calls for saving user/tokens.
            - [ ] Mock JWT creation.
            - [ ] Verify successful redirect to frontend, user creation/update (via mock calls), and app JWT cookie being set.
            - [ ] Test error scenarios (e.g., invalid `code`, Spotify token exchange failure).
    - [ ] Create `backend/tests/integration/api/test_playlist_endpoints.py`:
        - [ ] Test `GET /api/v1/playlists`: Mock `SpotifyIntegrationService.get_user_playlists`. Verify response schema and authentication requirement.
        - [ ] Test `GET /api/v1/playlists/{playlist_id}/details`: Mock `SpotifyIntegrationService.get_playlist_tracks_details`. Verify response schema and authentication requirement.
- [ ] **Task 4: Write Live Spotify API Contract Tests (Read-Only)** (AC: 3)
    - [ ] In `backend/tests/integration/integrations/test_spotify_service_live.py`:
        - [ ] Implement tests marked with `@pytest.mark.live_api_test` (or similar).
        - [ ] These tests will require valid (test/developer) Spotify credentials configured via environment variables.
        - [ ] Test `SpotifyIntegrationService.get_user_playlists` by making a live call and validating that the response can be successfully parsed into `UserPlaylistsResponseDto`.
        - [ ] Test `SpotifyIntegrationService.get_playlist_tracks_details` using a known, public, and stable Spotify playlist ID. Validate that the response (snapshot_id, tracks, artists) can be parsed into `PlaylistTracksDetailResponseDto`.
        - [ ] These tests should *only perform read operations* and handle potential Spotify API errors gracefully.
- [ ] **Task 5: Execute All Tests and Generate Coverage Report** (AC: 4, 5)
    - [ ] Run all `pytest` tests from the `backend/` directory.
    - [ ] Generate an HTML coverage report using `pytest-cov`.
    - [ ] Review the report to ensure critical paths are covered.
- [ ] **Task 6: Verification**
    - [ ] Ensure all ACs for Story 1.6 are met.

## Dev Technical Guidance

* **Testing Framework:** Use `Pytest` for all backend tests. Refer to `docs/tech-stack.md` and `docs/operational-guidelines.md` (Overall Testing Strategy).
* **FastAPI Testing:** Utilize FastAPI's `TestClient` for integration testing API endpoints.
    ```python
    # from fastapi.testclient import TestClient
    # from app.main import app # Assuming your FastAPI app instance is named 'app'
    # client = TestClient(app)
    # def test_health_check():
    #     response = client.get("/health")
    #     assert response.status_code == 200
    #     assert response.json() == {"status": "ok"}
    ```
* **Mocking:** Use `unittest.mock.patch` (as a decorator or context manager) or `pytest-mock` (for the `mocker` fixture) to mock dependencies in unit tests (e.g., `spotipy.Spotify` client methods, database session calls).
* **Live API Contract Tests:**
    * These tests are crucial for catching breaking changes in Spotify's API response structures.
    * Mark them appropriately (e.g., `@pytest.mark.live_api_test`) so they can be run selectively (e.g., less frequently in CI if they are slow or require special setup).
    * Securely manage any Spotify test credentials needed for these tests using environment variables (see `docs/environment-vars.md`). **Do not commit credentials.**
    * These tests **must not modify any Spotify data**. Use read-only operations only.
* **Test Structure:** Organize tests in `backend/tests/` mirroring the `backend/src/app/` structure (e.g., `tests/unit/services/`, `tests/integration/api/`).
* **Coverage:** Aim for high-quality tests covering logic and branches, not just raw line count. Use `pytest-cov` to generate reports (`pytest --cov=app --cov-report=html`).
* **Assertions:** Use clear and specific `assert` statements in Pytest.

## Story Progress Notes

### Agent Model Used: `<Agent Model Name/Version>`

### Completion Notes List
* {Leave blank for dev agent to fill}

### Change Log
* {Leave blank for dev agent to fill}