# Story 1.5: Basic Spotify Playlist Data Retrieval

## Status: Complete

## Story

- As the System, I want to be able to retrieve a list of the authenticated user's playlists and fetch the detailed contents (tracks, snapshot_id) of a specific user-selected playlist using the Spotify API, so that this data can be used as input for further analysis.

## Acceptance Criteria (ACs)

1.  The backend API (FastAPI application) `shall` provide a secure endpoint (e.g., `GET /api/v1/playlists`) that, using the authenticated user's stored and refreshed Spotify access token (from Story 1.4), fetches a list of the user's Spotify playlists.
2.  This list, returned by `GET /api/v1/playlists`, `shall` include at least the playlist ID, name, and track count for each playlist, matching the `UserPlaylistsResponseDto` and `SpotifyPlaylistSummaryDto` from `docs/data-models.md` (originally Main Arch Doc Sec 9b).
3.  The backend API `shall` provide another secure endpoint (e.g., `GET /api/v1/playlists/{playlist_id}/details`) that, given a Spotify `playlist_id`, retrieves all tracks from that playlist using the authenticated user's token.
4.  For each track retrieved by `GET /api/v1/playlists/{playlist_id}/details`, the data `shall` include at least its Spotify Track ID, name, and primary artist(s) information (name, ID).
5.  The `GET /api/v1/playlists/{playlist_id}/details` endpoint `shall` also retrieve and make available the playlist's current `snapshot_id` from Spotify.
6.  Basic error handling `shall` be implemented in the backend for these Spotify API interactions (e.g., handling `spotipy.exceptions.SpotifyException`).
7.  This includes handling potential issues like invalid playlist IDs, token errors (though token refresh should attempt to mitigate this), or Spotify API service unavailability, returning appropriate HTTP status codes (e.g., 401, 403, 404, 503) and error messages from the backend API.
8.  The functionality to list playlists and fetch specific playlist details `shall` be verifiable through API calls to the backend (e.g., using FastAPI's TestClient or manual tools like `curl`), with responses confirming the expected data structure and content.

## Tasks / Subtasks

- [ ] **Task 1: Define DTOs for Playlist Details** (AC: 4, 5, 8)
    - [ ] In `backend/src/app/core/models/dtos.py` (or similar), define:
        - [ ] `PlaylistTrackArtistDto(BaseModel): id: str | None = None, name: str`
        - [ ] `PlaylistTrackItemDto(BaseModel): spotify_track_id: str, name: str, artists: List[PlaylistTrackArtistDto]`
        - [ ] `PlaylistTracksDetailResponseDto(BaseModel): snapshot_id: str, tracks: List[PlaylistTrackItemDto], total_tracks: int`
    - [ ] Ensure these are distinct from DTOs specifically for the *managed* playlist if their structure significantly differs for this general retrieval context.
- [ ] **Task 2: Enhance/Implement `SpotifyIntegrationService`** (AC: 1-7)
    - [ ] In `backend/src/app/integrations/spotify_service.py` (or similar):
        - [ ] Implement `async def get_user_playlists(self, sp: spotipy.Spotify) -> UserPlaylistsResponseDto`:
            - [ ] Uses `sp.current_user_playlists()` to fetch playlists.
            - [ ] Handles pagination from Spotify to get all user playlists.
            - [ ] Maps results to `SpotifyPlaylistSummaryDto` objects.
        - [ ] Implement `async def get_playlist_tracks_details(self, sp: spotipy.Spotify, playlist_id: str) -> PlaylistTracksDetailResponseDto`:
            - [ ] Uses `sp.playlist(playlist_id, fields="snapshot_id,tracks.total")` to get snapshot ID and total tracks.
            - [ ] Uses `sp.playlist_items(playlist_id)` to fetch all tracks, handling pagination from Spotify.
            - [ ] For each track, extract Spotify ID, name, and primary artist(s) info (name, ID). Map to `PlaylistTrackItemDto`.
            - [ ] Construct and return `PlaylistTracksDetailResponseDto`.
        - [ ] Ensure both methods incorporate robust error handling for Spotipy exceptions, translating them to custom application exceptions if appropriate (e.g., `PlaylistNotFound`, `SpotifyApiError`).
        - [ ] Ensure these methods are called *after* ensuring the Spotify token is valid/refreshed (using logic from Story 1.4).
- [ ] **Task 3: Implement Backend API Endpoints** (AC: 1-8)
    - [ ] Create/update a FastAPI router (e.g., `backend/src/app/api/playlists_router.py`).
    - [ ] Implement `GET /api/v1/playlists` endpoint:
        - [ ] Depends on an authenticated Spotipy client instance (obtained after token refresh).
        - [ ] Calls `spotify_integration_service.get_user_playlists()`.
        - [ ] Returns `UserPlaylistsResponseDto`.
    - [ ] Implement `GET /api/v1/playlists/{playlist_id}/details` endpoint:
        - [ ] Depends on an authenticated Spotipy client instance.
        - [ ] Takes `playlist_id` as a path parameter.
        - [ ] Calls `spotify_integration_service.get_playlist_tracks_details()`.
        - [ ] Returns `PlaylistTracksDetailResponseDto`.
    - [ ] Ensure both endpoints are protected and require authentication (using dependency injection for user/token context from Story 1.4).
- [ ] **Task 4: Update API Documentation Stubs**
    - [ ] Add entries for the new `GET /api/v1/playlists/{playlist_id}/details` endpoint and its DTOs to the conceptual `docs/api-reference.md` content (or note that it needs to be formally added after implementation).
- [ ] **Task 5: Verification** (AC: 8)
    - [ ] Write unit tests for the new methods in `SpotifyIntegrationService` (mocking `spotipy.Spotify` calls).
    - [ ] Write integration tests for the new API endpoints using FastAPI's `TestClient` (may require mocking the Spotify token/client for focused testing, or limited live calls for verification if a test Spotify account is available).
    - [ ] Manually verify endpoints retrieve expected data using a tool like `curl` or Postman against the local dev server.

## Dev Technical Guidance

* **Spotipy Methods:**
    * For listing user playlists: `spotipy_client.current_user_playlists(limit=50)` - handle pagination using `results['next']`.
    * For playlist snapshot ID and total tracks: `spotipy_client.playlist(playlist_id, fields="snapshot_id,tracks.total")`.
    * For fetching playlist tracks: `spotipy_client.playlist_items(playlist_id, fields="items(track(id,name,artists(id,name))),next", limit=100)` - handle pagination.
* **DTOs (in `app/core/models/dtos.py`):**
    ```python
    from pydantic import BaseModel
    from typing import List, Optional

    # Re-using from Main Arch Doc / docs/data-models.md for consistency
    class SpotifyPlaylistSummaryDto(BaseModel):
        id: str # Spotify Playlist ID
        name: str
        track_count: int

    class UserPlaylistsResponseDto(BaseModel):
        playlists: List[SpotifyPlaylistSummaryDto]

    # New DTOs for this story
    class PlaylistTrackArtistDto(BaseModel):
        id: Optional[str] = None # Spotify Artist ID
        name: str

    class PlaylistTrackItemDto(BaseModel):
        spotify_track_id: str
        name: str
        artists: List[PlaylistTrackArtistDto]

    class PlaylistTracksDetailResponseDto(BaseModel):
        snapshot_id: str
        tracks: List[PlaylistTrackItemDto]
        total_tracks: int
    ```
* **SpotifyIntegrationService:** Ensure this service is injectable and handles the Spotipy client instance, including invoking token refresh logic from Story 1.4 before making calls.
* **Error Handling:** Catch `spotipy.exceptions.SpotifyException` and map to appropriate FastAPI `HTTPException` (e.g., 401, 403, 404, 503 for service unavailable).
* **Pagination with Spotipy:** Most list-returning Spotipy methods return a dictionary with `items` and `next`. Use `spotipy_client.next(results_page)` in a loop to get all items.
* **Authentication:** Endpoints must be protected. Use FastAPI dependencies to get the authenticated user and their valid Spotify access token (managed by logic from Story 1.4).

## Story Progress Notes

### Agent Model Used: `Gemini (via AI Developer Agent Persona)`

### Completion Notes List
* Defined Pydantic DTOs for playlist and track data.
* Implemented Spotify service functions to fetch playlists and track details, handling pagination and errors.
* Created a FastAPI dependency (`get_spotify_client_dependency`) to streamline authentication and client retrieval for secure endpoints.
* Added a new API router (`playlists_router.py`) with the required endpoints.
* Updated `main.py` to include the new router.
* Updated imports due to `spotify_client.py` rename to `spotify_service.py`.

### Change Log
* Created `backend/src/app/core/models/dtos.py`.
* Renamed `backend/src/app/integrations/spotify_client.py` to `spotify_service.py` and updated its content.
* Updated `backend/src/app/api/auth.py` (imports).
* Created `backend/src/app/api/dependencies.py`.
* Created `backend/src/app/api/playlists_router.py`.
* Updated `backend/src/app/main.py`.